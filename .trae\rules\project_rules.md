使用Claude3.7模型生成的小说章节，存放到仿写章节Claude下，并根据章节顺序升序排名，同是根据所写内容，为章节取一个极具吸引力点击率高的章节名。格式：第X章 章节名。
使用Gmini2.5Pro模型生成的小说章节，存放到仿写章节Gmini下，并根据章节顺序升序排名，同是根据所写内容，为章节取一个极具吸引力点击率高的章节名。格式：第X章 章节名。
每次修改章节内容时，均自动创建一个章节备份，放在同一个章节文件夹下，章节备份用来存放修改前的章节内容，格式：第X章（备） 章节名
每次修改完章节后，对修改前后进行对比评估，从当前市场流行的同类型网文的多个维度进行打分，精确到小数点后1位，满分10分，用来让创作者看到修改后小说质量的提升或者下降，并给予修改意见。
每次创建新章节后，对章节进行对比评估，从当前市场流行的同类型网文的多个维度进行打分，精确到小数点后1位，满分10分，用来让创作者看到修改后小说质量的提升或者下降，并给予修改意见。
每次写新章节的时候，需要将已经写的章节，自动添加到上下文中，避免续写时剧情不连贯。自动添加上下文的相对路径：仿写章节Claude/第X章 章节名
如果仿写内容中出现新角色，请帮我创建相应的角色卡，存放在相对路径：仿写角色卡